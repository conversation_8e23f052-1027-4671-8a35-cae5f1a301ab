<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">UI升级演示</text>
      <text class="header-subtitle">现代化设计风格展示</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 升级说明卡片 -->
      <view class="modern-card mb-4">
        <view class="upgrade-info">
          <text class="upgrade-title">🎉 UI升级完成</text>
          <text class="upgrade-desc">顶部导航栏和TabBar已升级为现代化设计风格</text>
        </view>
      </view>

      <!-- 功能展示 -->
      <view class="section-header mb-3">
        <text class="section-title">升级内容</text>
        <text class="section-subtitle">查看具体改进项目</text>
      </view>

      <view class="modern-grid">
        <!-- 导航栏升级 -->
        <view class="feature-card plan-card">
          <view class="card-icon">
            <text class="icon-text">📱</text>
          </view>
          <view class="card-content">
            <text class="card-title">导航栏升级</text>
            <text class="card-desc">渐变背景，现代化设计</text>
          </view>
        </view>

        <!-- TabBar升级 -->
        <view class="feature-card checkin-card">
          <view class="card-icon">
            <text class="icon-text">🎨</text>
          </view>
          <view class="card-content">
            <text class="card-title">TabBar升级</text>
            <text class="card-desc">毛玻璃效果，动画交互</text>
          </view>
        </view>

        <!-- 主题统一 -->
        <view class="feature-card monitor-card">
          <view class="card-icon">
            <text class="icon-text">🎯</text>
          </view>
          <view class="card-content">
            <text class="card-title">主题统一</text>
            <text class="card-desc">与主页面风格一致</text>
          </view>
        </view>

        <!-- 响应式设计 -->
        <view class="feature-card achievement-card">
          <view class="card-icon">
            <text class="icon-text">📐</text>
          </view>
          <view class="card-content">
            <text class="card-title">响应式设计</text>
            <text class="card-desc">适配不同屏幕尺寸</text>
          </view>
        </view>
      </view>

      <!-- 返回按钮 -->
      <view class="action-section">
        <button class="modern-button primary" @click="goBack">返回首页</button>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
    
    <!-- 自定义TabBar -->
    <ModernTabBar :current="0" />
  </view>
</template>

<script setup>
import ModernTabBar from '@/components/ModernTabBar/ModernTabBar.vue'

const goBack = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}
</script>

<style lang="scss">
.modern-page {
  padding-bottom: 120rpx; // 为自定义TabBar预留空间
}

.upgrade-info {
  text-align: center;
  
  .upgrade-title {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 16rpx;
  }
  
  .upgrade-desc {
    display: block;
    font-size: 26rpx;
    color: var(--gray-600);
    line-height: 1.5;
  }
}

.section-header {
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 8rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
  
  .section-subtitle {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
  }
}

.action-section {
  margin-top: 40rpx;
  text-align: center;
}

.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}
</style>
